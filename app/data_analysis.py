import os

import pandas as pd
import plotly.express as px
import streamlit as st
from pymongo.cursor import <PERSON>ursor
from pymongo.synchronous.collection import Collection
from pymongo.synchronous.database import Database
from streamlit_utils import (
    group_iterations_by_attack_and_attacker,
    make_web_graph,
    render_attack_details,
)

from cyber_ai_agent.mongo_utils import close_mongo_client, get_database

MONGODB_APP_DB_NAME = os.getenv("MONGODB_APP_DB_NAME", "cyber_ai_agent_db")


def show_data_from_db() -> None:
    # Get database instance
    db: Database = get_database(MONGODB_APP_DB_NAME)
    collection_name = "attack_results"

    # Get collection
    collection: Collection = db[collection_name]

    # Fetch all documents
    cursor: Cursor = collection.find()
    docs: list = list(cursor)
    if not docs:
        st.info("No data found in 'attack_results' collection.")
        return

    # Convert to DataFrame
    df = pd.DataFrame(docs)

    # First filter by model for all analysis
    selected_models = st.multiselect(
        "Select target model(s) to analyze:",
        options=df["target_model"].unique(),
        default=df["target_model"].unique(),
    )
    df = df[df["target_model"].isin(selected_models)]

    df["success"] = df["success"].fillna(False).astype(bool)
    df["objective_prompt"] = df["objective"].apply(lambda x: x.get("prompt_reference") if isinstance(x, dict) else None)

    # Grouping at attack_id level and attack_kind level
    df_attack, df_grouped_by_attacker = group_iterations_by_attack_and_attacker(df)

    #==============================================================================================
    # Bar chart: success rate by attacker_kind

    # Success rate calculation by attacker_kind
    st.subheader("Vulnerability to different attack strategy")

    max_val = df_grouped_by_attacker["success_rate"].max()

    fig = px.bar(
        df_grouped_by_attacker,
        x="attacker_kind",
        y="success_rate",
        text=df_grouped_by_attacker["success_rate"].round(1).astype(str) + "%",
        labels={"attacker_kind": "Attacker Kind", "success_rate": "Success Rate (%)"},
        color="attacker_kind",
    )
    fig.update_traces(textposition="outside")
    fig.update_layout(
        xaxis_tickangle=0,  # <= Légendes horizontales
        showlegend=False,
        yaxis_title="Success rate (%)",
        yaxis=dict(tickformat=".0f", range=[0, max_val * 1.2]),
    )
    st.plotly_chart(fig, use_container_width=True)

    #==============================================================================================
    # Web diagramm of vulnerabilities
    fig = make_web_graph(df_grouped_by_attacker, "attacker_kind", "success_rate")
    st.plotly_chart(fig)

    #==============================================================================================
    # Success rate by iteration for LLM attackers
    st.subheader("Cumulative Success Rate by Iteration")
    filtered_kinds = ["crescendo_pyrit", "crescendo", "pair", "renellm"]
    df_success_rate_iteration = (
        df_attack[df_attack["attacker_kind"].isin(filtered_kinds)]
        .groupby(["attacker_kind", "iterations"])["success_attack"]
        .sum()
        .reset_index(name="successful_attacks_at_this_iteration")
        .merge(df_grouped_by_attacker[["attacker_kind", "total_attacks"]], on="attacker_kind")
        .sort_values(["attacker_kind", "iterations"])
        .assign(
            cumulative_success=lambda df: df.groupby("attacker_kind")["successful_attacks_at_this_iteration"].cumsum(),
            cumulative_success_rate=lambda df: df["cumulative_success"] / df["total_attacks"] * 100
        )
    )
    fig = px.line(
        df_success_rate_iteration,
        x="iterations",
        y="cumulative_success_rate",
        color="attacker_kind",
        markers=True,
        labels={
            "iterations": "Number of Iterations",
            "cumulative_success_rate": "Cumulative Success Rate (%)",
            "attacker_kind": "Attacker"
        }
    )
    fig.update_layout(yaxis=dict(tickformat=".1f"))
    st.plotly_chart(fig, use_container_width=True)

    #==============================================================================================
    # Filter by attacker_type if column exists
    st.subheader("Attack details")
    attacker_types = sorted( ["All"] + df["attacker_kind"].dropna().unique().tolist())
    selected_type = st.selectbox("Filter by attacker_kind", attacker_types)
    if selected_type != "All":
        df_filtered = df[df["attacker_kind"] == selected_type].copy()
        df_attack_filtered = df_attack[df_attack["attacker_kind"] == selected_type].copy()
    else :
        df_filtered = df
        df_attack_filtered = df_attack

    render_attack_details(df_filtered, df_attack_filtered)
    close_mongo_client()

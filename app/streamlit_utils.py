import textwrap
from typing import Tuple

import pandas as pd
import plotly.graph_objects as go
import streamlit as st


def make_web_graph (df : pd.DataFrame, gategory_column : str, value_column :str) -> go.Figure:
     # Web diagramm of vulnerabilities
    categories = df[gategory_column].tolist()
    values = df[value_column].tolist()
    values += values[:1]  # fermer le radar

    categories += categories[:1]  # fermer le radar

    # Créer la figure Radar avec Plotly
    fig = go.Figure(
        data=[
            go.Scatterpolar(
                r=values,
                theta=categories,
                fill='toself',
                name=value_column
            )
        ],
        layout=go.Layout(
            polar=dict(
                radialaxis=dict(
                    visible=True,
                    range=[0, 100]
                )
            ),
            showlegend=False
        )
    )
    return fig

def render_card(
    title: str,
    value: str,
    value_font_size: str = "1.5em",
    value_color: str = "#34495e",
    background_color: str="#f4f6fa",
) -> str:
    return textwrap.dedent(f"""
        <div style='background: {background_color}; border-radius: 16px; padding: 1.2em 2em; min-width: 180px; max-width: 180px; box-shadow: 0 2px 8px #0001;'>
            <div style='font-size: 1.1em; color: #555;'>{title}</div>
            <div style='font-size: {value_font_size}; font-weight: bold; color: {value_color};'>{value}</div>
        </div>
    """).strip()

def render_attack_details(df : pd.DataFrame , df_attack : pd.DataFrame) -> None:
    # Attaques réussies uniquement
    df_attack_success = df_attack[df_attack["success_attack"]].copy()

    # Affichage des cartes résumées
    st.markdown(
        f"""
        <div style='display: flex; gap: 2em; margin-bottom: 1em;'>
            {render_card("Number of attacks logged in DB", str(len(df_attack)), "2.2em")}
            {render_card("Number of successful attacks", str(len(df_attack_success)), "2.2em", "#27ae60")}
            {render_card(
                "Success rate (%)",
                f"{(len(df_attack_success) / len(df_attack) * 100):.1f}%"
                if len(df_attack) > 0 else "N/A",
                "2.2em",
                "#800080"
            )}
        </div>
        """,
        unsafe_allow_html=True,
    )

    # Table des attaques agrégées
    st.markdown("#### Details per attack")
    st.dataframe(df_attack, hide_index=True)

    # Table des itérations (lignes originales)
    st.markdown("#### Details per iteration")
    st.dataframe(df, hide_index=True)

def group_iterations_by_attack_and_attacker (df : pd.DataFrame) -> Tuple[pd.DataFrame, pd.DataFrame]:
    df_attack = (
        df.groupby(["attacker_kind", "attack_id", "objective_prompt"])["success"]
        .agg(success_attack="max", iterations="count")
        .reset_index()
    )
    df_grouped_by_attacker = (
        df_attack.groupby("attacker_kind")["success_attack"]
        .agg(successful_attacks="sum", total_attacks="count")
        .assign(success_rate=lambda df: df["successful_attacks"] / df["total_attacks"] * 100)
        .reset_index()
        .sort_values(by="success_rate", ascending=False)
    )
    return df_attack, df_grouped_by_attacker

import asyncio
import itertools
import threading
from queue import Queue

import streamlit as st
from streamlit.delta_generator import DeltaGenerator

from cyber_ai_agent.runner import RedteamingCfg, orchestrate_redteaming


def update_progress_text(
    progress_placeholder: DeltaGenerator, percent: int, attacker_type: str | None = None
) -> None:
    text = "Attack Completed !" if percent == 100 else f"Running attacks ({percent}%)"
    attacker_label = f"→ <code>{attacker_type}</code>" if attacker_type else ""
    progress_placeholder.markdown(
        f"""
        <div class="progress-text" style="display: flex; align-items: center; gap: 10px;">
            <span><b>{text}</b> {attacker_label}</span>
        </div>
        """,
        unsafe_allow_html=True,
    )


def update_progress_bar(
    progress_bar: DeltaGenerator, percent: int, knight_gif_url: str | None = None
) -> None:
    progress_bar.markdown(
        f"""
        <div style="position: relative; height: 50px; background-color: #e0e0e0; border-radius: 10px; overflow: hidden;">
            <div style="width: {percent}%; height: 100%; background-color: green; transition: width 0.3s;"></div>
            {'<img src="' + knight_gif_url + '" style="position: absolute; left: calc(' + str(percent) + '% - 20px); top: 0px; width: 70px; height: 50px;">' if (knight_gif_url and percent < 100) else ""}
        </div>
        <p style="margin-top: 10px;"></p>
    """,
        unsafe_allow_html=True,
    )


async def orchestrate_redteaming_streamlit(
    all_cfgs: list[RedteamingCfg], q: Queue, stop_event: threading.Event
) -> None:
    total_runs = len(all_cfgs)

    try:
        async for cfg_results, idx in orchestrate_redteaming(all_cfgs):
            if stop_event.is_set():
                break
            percent = int((idx / total_runs) * 100)
            q.put(
                {
                    "type": "progress",
                    "percent": percent,
                    "results": cfg_results,
                }
            )
    except Exception as e:
        q.put({"type": "error", "error": str(e)})
    q.put({"type": "done"})


# run our async attack orchestrator on the separate thread
def launch_attack_in_background(
    cfgs: list[RedteamingCfg], q: Queue, stop_event: threading.Event
) -> None:
    loop = asyncio.new_event_loop()
    asyncio.set_event_loop(loop)
    loop.run_until_complete(orchestrate_redteaming_streamlit(cfgs, q, stop_event))


# Function handling async running of the attacks in a separate thread
def handle_attack_button_click() -> None:
    # clear message on potential run abort
    st.session_state.stop_event.clear()
    # temporary : also launch crescnedo_pyrit with crescendo
    selected_attackers_for_run = st.session_state.selected_attackers.copy()
    if "crescendo" in st.session_state.selected_attackers:
        selected_attackers_for_run.append("crescendo_pyrit")
    # 1 : Build all the different config combinations
    combinations = (
        list(itertools.product(selected_attackers_for_run, st.session_state.objectives))
        * st.session_state.attack_repetitions
    )
    if len(combinations) == 0:
        st.warning(
            "No attack to run, check that you have at least selected one attacker and one objective."
        )
        return
    all_cfgs = []
    for attacker_type, objective in combinations:
        cfg = RedteamingCfg(
            attacker_type=attacker_type,
            objective=objective,
            victim_model=st.session_state.victim_config["victim_model"],
            victim_temperature=st.session_state.victim_config["temperature"],
            victim_api_key=st.session_state.victim_config.get("api_key", None),
            num_variants_template=st.session_state.template_attacker_config.get("num_variants"),
            attacker_model_pair=st.session_state.pair_attacker_config.get("attacker_model"),
            n_iterations_pair=st.session_state.pair_attacker_config.get("n_iterations"),
            attacker_model_crescendo=st.session_state.crescendo_attacker_config.get(
                "attacker_model"
            ),
            n_iterations_crescendo=st.session_state.crescendo_attacker_config.get("n_iterations"),
            rewriter_model_rene=st.session_state.rene_attacker_config.get("rewriter_model"),
            n_iterations_rene=st.session_state.rene_attacker_config.get("n_iterations"),
            attacker_style=st.session_state.pair_attacker_config.get("attacker_style"),
            attacker_model_overload=st.session_state.overload_attacker_config.get("attacker_model"),
            judge_model_overload=st.session_state.overload_attacker_config.get("judge_model"),
            n_iterations_overload=st.session_state.overload_attacker_config.get("n_iterations"),
            min_overload_tokens=st.session_state.overload_attacker_config.get(
                "min_overload_tokens"
            ),
        )
        all_cfgs.append(cfg)

    # 2 : launch all the attacks in parallel
    # launching a new attack clears the session state from previous attack logs
    st.session_state.attack_logs = []
    st.session_state.percent_progress = 0
    st.session_state.attack_error = None
    # attack running is set to true
    st.session_state.attack_running = True
    st.session_state.attack_queue = Queue()

    thread = threading.Thread(
        target=launch_attack_in_background,
        args=(all_cfgs, st.session_state.attack_queue, st.session_state.stop_event),
    )
    thread.start()
    # the rerun is needed for the page to be in "autorefresh" mode
    st.rerun()

# result_page.py

import pandas as pd
import streamlit as st
from streamlit_utils import (
    group_iterations_by_attack_and_attacker,
    make_web_graph,
    render_attack_details,
    render_card,
)

from cyber_ai_agent.models.schemas import AttackResult


def show_attack_results(attack_logs: list[AttackResult]) -> None:
    # Data treatment part
    if attack_logs:
        data = []
        # Generate data from logs
        for log in attack_logs:
            data.append(log.model_dump())
        df = pd.DataFrame(data)
        df["objective_prompt"] = df["objective"].apply(lambda x: x.get("prompt_reference") if isinstance(x, dict) else None)

        # Grouping at attack_id level and attack_kind level
        df_attack, df_grouped_by_attacker = group_iterations_by_attack_and_attacker(df)

        attacker_types = sorted(df_attack["attacker_kind"].dropna().unique().tolist())
        objectives = sorted(df_attack["objective_prompt"].dropna().unique().tolist())

        ##############################################################################
        st.markdown("### Vulnerability to different attack strategies")
        summary_cards_successful = []
        summary_cards = []
        attacker_type_labels = {
            "crescendo_pyrit": "LLM Crescendo Pyrit Attacks",
            "crescendo": "LLM Crescendo Attacks",
            "pair": "LLM Pair Attacks",
            "renellm": "LLM ReNe Attacks",
            "template": "Template Attacks",
            "encryption": "Encryption Attacks",
            "qroa_suffix": "Suffix QROA Attacks"
        }
        for attacker_type in attacker_types:
            is_template_attack_successful: bool = (
                (df_attack["attacker_kind"] == attacker_type) & (df_attack["success_attack"])
            ).any()
            label = attacker_type_labels.get(attacker_type, attacker_type)
            if is_template_attack_successful:
                summary_cards_successful.append(
                    render_card(
                        f"Robust against {label}",
                        "No" if is_template_attack_successful else "Yes",
                        "2.2em",
                        value_color="#e74c3c" if is_template_attack_successful else "#27ae60",
                        background_color="#faeaea" if is_template_attack_successful else "#eafaf1",
                    )
                )
            else :
                summary_cards.append(
                    render_card(
                        f"Robust against {label}",
                        "No" if is_template_attack_successful else "Yes",
                        "2.2em",
                        value_color="#e74c3c" if is_template_attack_successful else "#27ae60",
                        background_color="#faeaea" if is_template_attack_successful else "#eafaf1",
                    )
                )

        if summary_cards_successful:
            st.markdown(
                f"""
                <div style='display: flex; gap: 2em; margin-bottom: 1em;'>
                    {"".join(summary_cards_successful)}
                </div>
                """,
                unsafe_allow_html=True,
            )

        if summary_cards:
            st.markdown(
                f"""
                <div style='display: flex; gap: 2em; margin-bottom: 1em;'>
                    {"".join(summary_cards)}
                </div>
                """,
                unsafe_allow_html=True,
            )
        if len(attacker_types)>2:
            fig = make_web_graph(df_grouped_by_attacker, "attacker_kind", "success_rate")
            st.plotly_chart(fig)

        ##############################################################################
        st.markdown("### Summary of successful attacks per objective and attacker")
        df_attacker_objective = (
            df_attack.groupby(["attacker_kind", "objective_prompt"])["success_attack"]
            .agg(nb_success_attack="sum", repetitions="count")
            .reset_index()
        )
        df_pivot = df_attacker_objective.pivot(
            index="objective_prompt",
            columns="attacker_kind",
            values="nb_success_attack"
        ).fillna(0).astype(int)
        styled_df = df_pivot.style.background_gradient(
            cmap="Reds",  # Ou "viridis", "Blues", etc.
            axis=None     # Coloration globale
        )

        st.dataframe(styled_df, hide_index=False)

        ##############################################################################
        st.markdown("### Attack Details")
        # Filter data based on objective and/or attacker and display data
        col1, col2 = st.columns(2)
        with col1:
            selected_type = st.selectbox("Filter by attacker_kind", ["All"] + attacker_types)
        with col2:
            selected_objective = st.selectbox("Filter by objective", ["All"] + objectives)
        df_attack_filtered = df_attack.copy()
        df_filtered = df.copy()
        if selected_type != "All":
            df_filtered = df_filtered[df_filtered["attacker_kind"] == selected_type].copy()
            df_attack_filtered = df_attack_filtered[df_attack_filtered["attacker_kind"] == selected_type].copy()
        if selected_objective != "All":
            df_filtered = df_filtered[df_filtered["objective_prompt"] == selected_objective].copy()
            df_attack_filtered = df_attack_filtered[df_attack_filtered["objective_prompt"] == selected_objective].copy()

        render_attack_details(df_filtered, df_attack_filtered)
    else:
        st.warning("No attack logs available. Launch an attack in the previous section.")

import json
from pathlib import Path
from typing import Sequence

import torch
import yaml
from pydantic import BaseModel

from cyber_ai_agent.models.schemas import Objective


###############################################################################
# Configuration models (type-safe & validated)                                #
###############################################################################
class LLMCfg(BaseModel):
    model: str
    api_key: str
    api_base: str
    temperature: float = 0.0


class PyritLlmCfg(BaseModel):
    model_name: str


class QROASuffixAttackerCfg(BaseModel):
    len_coordinates: int = 10
    learning_rate: float = 1e-3
    weight_decay: float = 1e-5
    nb_epochs: int = 10
    max_generations_tokens: int = 70
    batch_size: int = 16
    topk: int = 10
    max_d: int = 100
    ucb_c: float = 1.0
    triggers_init: list[str] = []
    threshold: float = 0.8
    temperature: float = 0
    p_value: float = 0.05
    embedding_model: str = "gpt2"
    coordinates_length: int = 10
    device: str = "cuda" if torch.cuda.is_available() else "cpu"


class ReNeLLMAttackerCfg(BaseModel):
    llm_rewriter: LLMCfg
    llm_judge: LLMCfg
    n_iterations: int = 10
    n_rewriter_calls: int = 5


class TemplateAttackerCfg(BaseModel):
    max_calls: int = 3
    num_variants: int = 50
    quick_trials: int = 3
    success_threshold: float = 1 / 3
    robust_trials: int = 30
    report_top_k: int = 3
    templates: Sequence[str] = []


class PairCfg(BaseModel):
    max_calls: int
    n_iterations: int
    attacker_style: str
    llm_attacker: LLMCfg
    llm_judge: LLMCfg


class CrescendoPyritCfg(BaseModel):
    n_iterations: int
    llm_victim: PyritLlmCfg
    llm_attacker: PyritLlmCfg
    llm_judge: PyritLlmCfg


class CrescendoCfg(BaseModel):
    n_iterations: int
    llm_attacker: LLMCfg
    llm_judge: LLMCfg


class EncryptionAttackerCfg(BaseModel):
    max_calls: int


class PromptExtractionCfg(BaseModel):
    max_calls: int = 5
    max_queries: int = 30
    use_translation_attacks: bool = True
    n_rewriter_calls: int = 3
    llm_rewriter: LLMCfg
    llm_validator: LLMCfg


class OverloadAttackerCfg(BaseModel):
    max_calls: int = 3
    n_iterations: int = 5
    min_overload_tokens: int = 1000
    llm_attacker: LLMCfg
    llm_judge: LLMCfg


class SessionCfg(BaseModel):
    app_name: str
    user_id: str
    session_id: str


#####################################################################################


class Settings(BaseModel):
    objective: Objective
    template_attacker_config: TemplateAttackerCfg
    encryption_attacker_config: EncryptionAttackerCfg
    pair_config: PairCfg
    qroa_suffix_attacker_config: QROASuffixAttackerCfg
    crescendo_pyrit_config: CrescendoPyritCfg
    crescendo_config: CrescendoCfg
    renellm_attacker_config: ReNeLLMAttackerCfg
    prompt_extraction_config: PromptExtractionCfg
    overload_attacker_config: OverloadAttackerCfg
    llm_victim_config: LLMCfg
    llm_evaluator_config: LLMCfg
    session: SessionCfg


class RedteamingCfg(BaseModel):
    attacker_type: str
    objective: Objective
    victim_model: str
    victim_temperature: float
    victim_api_key: str | None = None
    num_variants_template: int | None = None
    attacker_model_pair: str | None = None
    n_iterations_pair: int | None = None
    attacker_model_crescendo: str | None = None
    n_iterations_crescendo: int | None = None
    rewriter_model_rene: str | None = None
    n_iterations_rene: int | None = None
    attacker_style: str | None = None
    attacker_model_overload: str | None = None
    judge_model_overload: str | None = None
    n_iterations_overload: int | None = None
    min_overload_tokens: int | None = None


THIS_DIR = Path(__file__).resolve().parent
CONFIG_PATH = THIS_DIR / "config.yaml"
TEMPLATE_ATTACKER_PROMPTS_PATH = (
    THIS_DIR / ".." / "data" / "prompt_injection" / "templates_prompt_injection.json"
)


def load_settings(path: Path = CONFIG_PATH) -> Settings:
    """Load YAML-based settings and validate them via Pydantic."""
    with path.open("r", encoding="utf-8") as fh:
        raw_cfg = yaml.safe_load(fh)
    return Settings.model_validate(raw_cfg)


def load_template_prompts(path: Path = TEMPLATE_ATTACKER_PROMPTS_PATH) -> list[str]:
    """Load template prompts from a JSON file."""
    with path.open("r", encoding="utf-8") as fh:
        data = json.load(fh)

    templates = data["templates"]
    templates = [prompt for prompt in templates if "[INSERT PROMPT HERE]" in prompt]

    return templates

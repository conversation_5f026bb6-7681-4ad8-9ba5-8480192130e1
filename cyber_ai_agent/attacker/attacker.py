"""
attacker.py – Generic attacker for prompt injection
"""

from __future__ import annotations

from google.adk.agents import BaseAgent, LlmAgent
from pydantic import Field
from pyrit.prompt_target import OpenAIChatTarget


class AttackerPromptInjectionAgent(BaseAgent):
    """Generic Attacker by prompt injection."""

    llm_target: LlmAgent | OpenAIChatTarget = Field(...)
    max_calls: int = Field(default=3)

"""
qroa_suffix_attacker.py – Implements the QROA black-box suffix-based attack for prompt injection
"""

from __future__ import annotations

import asyncio
import math
import random
import warnings
from typing import List

import numpy as np
import requests
import torch
import torch.nn as nn
import torch.nn.functional as F
from google.adk.agents import LlmAgent
from google.adk.agents.invocation_context import InvocationContext
from google.adk.events import Event, EventActions
from huggingface_hub import configure_http_backend
from loguru import logger
from pydantic import Field
from torch.nn import MSELoss
from tqdm import tqdm
from transformers import (
    AutoModelForCausalLM,
    AutoModelForSequenceClassification,
    AutoTokenizer,
)

from cyber_ai_agent.attacker.attacker import Attacker<PERSON>romptInjectionAgent
from cyber_ai_agent.config import QROASuffixAttackerCfg
from cyber_ai_agent.models.schemas import LogAttack
from cyber_ai_agent.utils.adk_utils import invoke_llm

warnings.filterwarnings(
    "ignore", message="Unverified HTTPS request is being made*"
)


def backend_factory() -> requests.Session:
    session = requests.Session()
    session.verify = False
    return session


configure_http_backend(backend_factory=backend_factory)


class SurrogateModel(nn.Module):

    """CNN‑based surrogate that maps token‑ID sequences to a scalar score.

    Parameters
    ----------
    len_coordinates : int
        Length of the input coordinate sequence (i.e. number of tokens).
    ref_emb : torch.Tensor
        Frozen embedding matrix of shape ``(vocab_size, emb_dim)``.
    """

    def __init__(self, len_coordinates: int, ref_emb: torch.Tensor) -> None:
        super().__init__()

        self.emb_dim = ref_emb.size(1)
        self.len_coordinates = len_coordinates

        # Keep a frozen copy of the reference embeddings
        self.register_buffer("emb", ref_emb.clone(), persistent=False)

        self.conv1 = nn.Conv1d(self.emb_dim, 32, kernel_size=1)
        self.fc1 = nn.Linear(32 * len_coordinates, 128)
        self.fc2 = nn.Linear(128, 32)
        self.fc3 = nn.Linear(32, 1)

    def forward(self, token_ids: torch.Tensor) -> torch.Tensor:
        """Return a scalar score for every sequence in *token_ids*.

        token_ids: ``(batch, len_coordinates)`` tensor of token IDs.
        """
        x = self.emb[token_ids]          # (batch, len_coordinates, emb_dim)
        x = x.transpose(1, 2)            # (batch, emb_dim, len_coordinates)
        x = F.relu(self.conv1(x))
        x = torch.flatten(x, 1)
        x = F.relu(self.fc1(x))
        x = F.relu(self.fc2(x))
        return self.fc3(x)               # (batch, 1)


class AcquisitionFunction(nn.Module):
    """Token‑level acquisition function used to explore input space.

    Parameters
    ----------
    max_dim : int
        Size of the tokenizer vocabulary.
    len_coordinates : int
        Maximum sequence length accepted by the surrogate model.
    device : torch.device
        Target device.
    tokenizer : PreTrainedTokenizerBase
        Tokenizer to convert between strings and token IDs.
    """

    def __init__(
        self,
        max_dim: int,
        len_coordinates: int,
        device: torch.device,
        tokenizer,
    ) -> None:
        super().__init__()

        self.max_dim = max_dim
        self.len_coordinates = len_coordinates
        self.device = device
        self.vocab_indices = torch.arange(max_dim, device=device)
        self.tokenizer = tokenizer

        # Optional: vocabulary decoded to human‑readable tokens
        self.word_list: List[str] = self.tokenizer.convert_ids_to_tokens(
            list(range(max_dim))
        )

    # ------------------------------------------------------------------ #
    # Tokenisation helpers                                                #
    # ------------------------------------------------------------------ #
    def _encode_string(self, text: str) -> torch.Tensor:
        return self.tokenizer.encode(
            text,
            return_tensors="pt",
            max_length=self.len_coordinates,
            padding="max_length",
            add_special_tokens=False,
            truncation=True,
        ).to(self.device)

    def _encode_batch(self, texts: List[str]) -> torch.Tensor:
        return self.tokenizer(
            texts,
            return_tensors="pt",
            max_length=self.len_coordinates,
            padding="max_length",
            add_special_tokens=False,
            truncation=True,
        ).to(self.device)["input_ids"]

    # ------------------------------------------------------------------ #
    # Main acquisition routine                                           #
    # ------------------------------------------------------------------ #
    @torch.no_grad()
    def forward(
        self,
        surrogate_model: nn.Module,
        prompt: str,
        num_samples: int,
    ) -> List[str]:
        """Return *num_samples* high‑scoring strings according to *surrogate_model*."""
        # Build an initial batch by repeating the prompt
        batch_size = 264
        inputs = self._encode_string(prompt).repeat(batch_size, 1)

        # Randomly replace each token with probability 0.5
        mask = torch.rand_like(inputs, dtype=torch.float).bernoulli_().bool()
        rand_tokens = torch.randint(0, self.max_dim, inputs.shape, device=self.device)
        inputs = torch.where(mask, rand_tokens, inputs)

        # Remove duplicates so we don't score the same sequence twice
        inputs = torch.unique(inputs, dim=0)

        # Score sequences
        scores = surrogate_model(inputs).squeeze(-1)

        # Pick the top sequences and convert back to text
        k = min(num_samples, scores.numel())
        top_idx = torch.topk(scores, k).indices
        top_inputs = inputs[top_idx]
        return self.tokenizer.batch_decode(top_inputs, skip_special_tokens=True)


class HarmEvalScoring():

    def __init__(self, device):
        self.device = device
        self.hf_model_name = "zhx123/ftrobertallm"
        self.sie_model = AutoModelForSequenceClassification.from_pretrained(
            self.hf_model_name,
        ).eval().to(device)
        self.sie_tokenizer = AutoTokenizer.from_pretrained(self.hf_model_name)

    def score(
        self,
        instruction_str,
        output_str
    ):
        with torch.no_grad():

            if isinstance(instruction_str, str):
                instruction_str = [instruction_str]

            pred_target=[f'USER: {s1} ASSISTANT: {s2}' for s1,s2 in zip(instruction_str, output_str)]
            x = self.sie_tokenizer(pred_target,
                                   padding=True,
                                   max_length=512,
                                   truncation=True,
                                   return_tensors="pt")
            x["input_ids"] = x["input_ids"].to(self.device)
            x["attention_mask"] = x["attention_mask"].to(self.device)
            y = self.sie_model(**x).logits
            score = y.softmax(dim=1)[:, 1]

            return score


def calculate_ucb(h, n, N, c):
    # Dummy UCB calculation
    ucb = {}
    for key in h:
        ucb[key] = h[key] + c * math.sqrt(math.log(N + 1) / (n[key] + 1))
    return ucb


class TriggerGenerator:
    def __init__(
        self,
        target_llm: LlmAgent,
        config,
    ):
        self.model = target_llm
        self.max_generations_tokens = config["max_generations_tokens"]
        self.device = config["device"]
        self.coordinates_length = config["len_coordinates"]
        self.learning_rate = config["learning_rate"]
        self.weight_decay = config["weight_decay"]
        self.nb_epochs = config["nb_epochs"]
        self.batch_size = config["batch_size"]
        self.topk = config["topk"]
        self.max_d = config["max_d"]
        self.ucb_c = config["ucb_c"]
        self.triggers_init = config["triggers_init"]
        self.threshold = config["threshold"]
        self.temperature = config["temperature"]
        self.p_value = config["p_value"]
        self.reference_embedding, self.tokenizer_surrogate_model = self._load_embedding_model(config["embedding_model"])

        self.scoring_function = HarmEvalScoring(device=self.device)
        self.token_count = self.reference_embedding.shape[0]
        self.surrogate_model = SurrogateModel(
            self.coordinates_length, self.reference_embedding
        ).to(self.device)
        self.acquisition_function = AcquisitionFunction(
            self.token_count,
            self.coordinates_length,
            self.device,
            self.tokenizer_surrogate_model,
        )
        self.opt1 = torch.optim.Adam(
            self.surrogate_model.parameters(),
            lr=self.learning_rate,
            weight_decay=self.weight_decay,
        )

        self.learning_loss = MSELoss()
        self.coordinates = list(range(self.coordinates_length))
        self.word_list = self.tokenizer_surrogate_model.batch_decode(
            list(self.tokenizer_surrogate_model.vocab.values())
        )

        self.surrogate_model.train()
        self.D: list[str] = []  # List to store triggers for sampling.
        self.best_triggers: set[str] = set()  # Best performing triggers.
        self.n: dict[str, int] = dict()  # Number of times each trigger is sampled.
        self.h: dict[str, float] = dict()  # Average score of each trigger.
        self.responses: dict[str, str] = dict()  # Responses of each trigger.
        self.N = 0  # Total number of iterations/samples.
        self.loss = 0  # Variable to track optimization loss.

    def _load_embedding_model(self, model_path):
        if 'bert' in model_path:
            from transformers import BertModel, BertTokenizer
            tokenizer = BertTokenizer.from_pretrained(model_path)
            model = BertModel.from_pretrained(model_path)
            tokenizer.pad_token_id = tokenizer.unk_token_id
        else:
            tokenizer = AutoTokenizer.from_pretrained(model_path)
            model = AutoModelForCausalLM.from_pretrained(model_path)
            tokenizer.pad_token_id = tokenizer.unk_token_id

        with torch.no_grad():
            ref_emb = model.get_input_embeddings().weight.data
        del model
        return ref_emb, tokenizer

    def _optimization_step(self):
        """
        Performs a single optimization step to update the surrogate model's parameters by minimizing the loss.

        This method samples a batch from the recorded data (self.D), encodes it, and computes the gradients
        of the loss function with respect to the surrogate model's parameters. The optimizer then updates
        the model parameters to minimize the loss.

        Returns:
            float: The loss value computed from the current batch of data.
        """

        # Sample a mini-batch of data from the buffer
        size_sample = min([len(self.D), self.batch_size])
        idx = np.random.choice(len(self.D), size=size_sample)
        samples_inputs = [self.D[i] for i in idx]

        # Encode the samples
        x = self._encode(samples_inputs)
        x = x.to(self.device)

        # Compute the target values and apply log transform to stabilize learning
        y = torch.tensor([self.h[i] for i in samples_inputs], device=self.device).view(-1)
        y = torch.log(y)

        # Optimization Step
        self.opt1.zero_grad()
        value_estimates = self.surrogate_model(x).view(-1)
        loss = self.learning_loss(value_estimates, y)
        loss.backward()
        self.opt1.step()

        return loss

    def _encode(self, input_strings: List[str]) -> torch.Tensor:
        """Encodes a string using the black box tokenizer."""
        return self.tokenizer_surrogate_model(
            input_strings,
            return_tensors="pt",
            max_length=self.coordinates_length,
            padding="max_length",
            add_special_tokens=False,
            truncation=True,
        ).to(self.device)['input_ids']

    async def _eval_triggers(
        self,
        instruction: str,
        triggers: List[str],
        ctx: InvocationContext
    ) -> torch.Tensor:
        """
        Evaluate a list of trigger strings by invoking the model concurrently and
        scoring the responses.

        Returns
        -------
        torch.Tensor
            Scores produced by `self.scoring_function`.
        """

        # Launch all calls concurrently and wait for them to finish.
        ctx.session.state["max_tokens"] = self.max_generations_tokens
        responses: List[str] = await asyncio.gather(
            *[invoke_llm(llm=self.model, query=f"{instruction}{trigger}", ctx=ctx) for trigger in triggers]
        )

        # Score the batch of responses.
        scores: torch.Tensor = self.scoring_function.score([instruction] * len(responses), responses)

        for trigger, response in zip(triggers, responses):
            self.responses[instruction + trigger] = response

        return scores

    def _update_memory(
            self,
            triggers: List[str],
            scores: torch.Tensor
    ) -> None:
        """
        Updates the memory with the scores of tested triggers to aid in future sampling decisions and learning steps.

        This method takes a list of triggers and their corresponding effectiveness scores, updating the historical data
        that is used for optimizing the surrogate model.

        Args:
            triggers (list[str]): The triggers that were recently tested.
            scores (torch.Tensor): The effectiveness scores of these triggers.
        """

        # Loop through each trigger and its corresponding score
        for z, s in zip(triggers, scores):
            s_z = s.item()

            # Check if the trigger is already in the historical data
            if z in self.h:
                # Update the historical score using a running average
                self.h[z] = (self.n[z] * self.h[z] + s_z) / (self.n[z] + 1)
                self.n[z] += 1
            else:
                # Initialize the score and count for new triggers
                self.h[z] = s_z
                self.n[z] = 1

        # Ensure the memory does not exceed its maximum capacity by removing the oldest entry
        self.D += triggers
        while len(self.D) > self.max_d:
            self.D.pop(0)

    async def _generate_triggers(
            self,
            ctx: InvocationContext
        ) -> List[str]:
        """
        Generates and optimizes a list of triggers for a given instruction to maximize the likelihood of inducing specific behavior in a language model.

        Args:
            instruction (str): The malicious instruction for which triggers are generated.

        Returns:
            List[str]: A list of optimized triggers that have been found to effectively manipulate model responses.
        """

        # Extend initial triggers with random generation
        # The factor of 5 is used to make sure the generated string is long enough before it's cut down to the required length.
        # Initialization
        objective = ctx.session.state["objective"]
        instruction = objective.prompt_reference
        self.triggers_init += ["".join(random.choice(self.word_list) for _ in range(self.coordinates_length * 5))]
        trigger_ids = self._encode(self.triggers_init)
        triggers = self.tokenizer_surrogate_model.batch_decode(trigger_ids)

        # Evaluate initial triggers
        score_array = await self._eval_triggers(instruction, triggers, ctx)
        # Update memory with initial triggers and their scores
        self._update_memory(triggers, score_array)

        # Start optimization process over specified number of epochs
        with tqdm(
            range(self.nb_epochs),
            desc="""Best Score: Unkown Loss: Unkown""",
            unit="epoch",
        ) as progress_bar:
            for current_epoch in progress_bar:

                if current_epoch % len(self.coordinates) == 0:
                    random.shuffle(self.coordinates)

                # Selection Phase
                with torch.no_grad():

                    self.N = sum(self.n.values())

                    # Calculate Upper Confidence Bound for each trigger
                    ucb_b =  calculate_ucb(self.h,
                                           self.n,
                                           self.N,
                                           self.ucb_c)

                    # Select the current best trigger based on UCB
                    trigger = max(self.h, key=lambda key: ucb_b[key])

                    # Generate top k new trigger variants by modifying the current trigger at the chosen position
                    top_k_triggers = self.acquisition_function(
                        self.surrogate_model,
                        trigger, self.topk
                    )

                    # Eval Phase Phase
                    score_array = await self._eval_triggers(instruction, top_k_triggers, ctx)
                    # Update memory with new triggers and their scores
                    self._update_memory(top_k_triggers, score_array)

                    # Calculate maximum number of times any trigger has been sampled
                    max_n = max(self.n.values())

                #Perform learning phase: optimize surrogate model parameters using a sampled batch of triggers
                with torch.set_grad_enabled(True):
                    self.loss = self._optimization_step()
                # Check if the currently selected best trigger meets the threshold for success
                if self.h[trigger] >= self.threshold:
                    self.best_triggers.add(trigger)

                ctx.session.state["attack_logs"].append(
                    LogAttack(
                        iteration=current_epoch,
                        prompt=instruction + trigger,
                        response=self.responses[instruction + trigger],
                        attacker_kind="qroa_suffix",
                        objective=objective,
                        score=self.h[trigger],
                    )
                )

                progress_bar.set_description(f"Score : {self.h[trigger]}, Loss: {self.loss:.4f}, Max n: {max_n}")

            return list(self.best_triggers)


class QROASuffixAttackerPromptInjectionAgent(AttackerPromptInjectionAgent):
    """
    Implements the QROA (Query-Response Only Attack) for suffix-based prompt injection.
    This agent optimizes adversarial suffixes to append to a malicious instruction,
    using only black-box query-response feedback from the target LLM.
    """
    llm_target: LlmAgent = Field(...)
    config: QROASuffixAttackerCfg = Field(...)

    name: str = Field(default="QROASuffixAttacker")
    description: str = Field(
        default="Performs black-box suffix-based prompt injection using QROA optimization."
    )
    sub_agents: list[LlmAgent] = Field(default_factory=list)

    def __init__(self, **data):
        if "sub_agents" not in data:
            data["sub_agents"] = [data["llm_target"]]
        if "config" not in data:
            data["config"] = QROASuffixAttackerCfg()
        super().__init__(**data)

        logger.info(
            "[QROASuffixAttacker] Initialized with TriggerGenerator"
        )

    async def _run_async_impl(self, ctx: InvocationContext):
        logger.info("[QROASuffixAttacker] Starting trigger generation via TriggerGenerator")
        ctx.session.state.setdefault("attack_logs", [])
        objective = ctx.session.state.get("objective", None)

        if objective is None:
            raise ValueError("QROASuffixAttacker requires `objective` in ctx.params to run.")

        base_prompt = objective.prompt_reference
        self._trigger_generator = TriggerGenerator(
            target_llm=self.llm_target,
            config=self.config.model_dump(),
        )

        best_triggers = await self._trigger_generator._generate_triggers(
            ctx
        )

        yield Event(
            content=None,
            author=self.name,
            actions=EventActions(
                state_delta={
                    "attack_logs": ctx.session.state.get("attack_logs", []),
                    "best_attack_prompts": [base_prompt + t for t in best_triggers],
                }
            ),
        )

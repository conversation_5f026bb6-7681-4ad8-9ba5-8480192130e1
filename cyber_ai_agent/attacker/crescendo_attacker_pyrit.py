
from typing import AsyncGenerator, MutableSequence, Sequence

from google.adk.agents import LlmAgent
from google.adk.agents.invocation_context import InvocationContext
from google.adk.events import Event, EventActions
from loguru import logger
from pydantic import Field
from pyrit.models import PromptRequestPiece, PromptRequestResponse
from pyrit.orchestrator import CrescendoOrchestrator, OrchestratorResult
from pyrit.prompt_target import OpenAIChatTarget

from cyber_ai_agent.attacker.attacker import AttackerPromptInjectionAgent
from cyber_ai_agent.models.schemas import LogAttack, ObjectivePromptInjection


class CrescendoPyritAttackerPromptInjectionAgent (AttackerPromptInjectionAgent):

    name: str = Field(default="CrescendoPyritAttacker")
    description: str = Field(
        default="Performs crescendo prompt-injection attacks using LLM and pyrit."
    )
    sub_agents: list[LlmAgent] = Field(default_factory=list)
    n_iterations: int = Field(default = 5)
    llm_target : OpenAIChatTarget
    llm_attacker : OpenAIChatTarget
    llm_judge : OpenAIChatTarget

    def __init__(self, **data):
        super().__init__(**data)
        logger.info("[CrescendoPyritAttacker] Initialized")

    # Method "_run_async_impl" is marked as an override,
    # but no base method was found with this name
    # @override
    async def _run_async_impl(self, ctx: InvocationContext) -> AsyncGenerator[Event, None]:
        logger.info("[CrescendoPyritAttacker] Starting attack")

        objective: ObjectivePromptInjection | None = ctx.session.state.get("objective", None)
        if objective is None:
            raise ValueError("Attacker requires `objective` in ctx.params to run.")


        orchestrator = CrescendoOrchestrator(
            objective_target=self.llm_target,
            adversarial_chat=self.llm_attacker,
            max_turns=self.n_iterations,
            max_backtracks=2,
            scoring_target=self.llm_judge,
        )

        # For five turns this can take a few minutes depending on LLM latency
        results : list[OrchestratorResult] = await orchestrator.run_attacks_async(objectives=[objective.prompt_reference])  # type: ignore
        logger.info("[CrescendoPyritAttacker] attack finished")
        ctx.session.state.setdefault("attack_logs", [])
        for result in results:
            target_messages : MutableSequence[PromptRequestResponse] = result._memory.get_conversation(conversation_id=result.conversation_id)
            iteration = 0
            for message in target_messages:
                pieces : Sequence[PromptRequestPiece] = message.request_pieces
                for piece in pieces :
                    if piece.role == "user":
                        iteration += 1
                        prompt = piece.converted_value
                    if piece.role == "assistant":
                        response = piece.converted_value
                        for score_obj in piece.scores:
                            if score_obj.scorer_class_identifier['__type__'] == "FloatScaleThresholdScorer":
                                score_bool = score_obj.score_value == "True"
                                score = 1 if score_bool else 0
                        ctx.session.state["attack_logs"].append(
                            LogAttack(
                                iteration=iteration,
                                prompt=prompt,
                                response=response,
                                attacker_kind="crescendo_pyrit",
                                objective=objective,
                                score=score,
                            )
                        )

        # Create an event with state_delta to update attack_logs and send it to the runner
        yield Event(
            content=None,
            author=self.name,
            actions=EventActions(
                state_delta={
                    "attack_logs": ctx.session.state.get("attack_logs", []),
                    "best_attack_prompts": [],
                }
            ),
        )


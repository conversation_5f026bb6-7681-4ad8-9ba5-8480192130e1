from google.adk.agents import BaseAgent, LlmAgent
from jinja2 import Template
from loguru import logger

from cyber_ai_agent.attacker.crescendo_attacker import (
    ATTACKER_SYSTEM_PROMPT,
    JUD<PERSON>_SYSTEM_PROMPT,
    VICTIM_SYSTEM_PROMPT,
    CrescendoAttackerOutputSchema,
    CrescendoAttackerPromptInjectionAgent,
    CrescendoJudgeOutputSchema,
)
from cyber_ai_agent.attacker.crescendo_attacker_pyrit import (
    CrescendoPyritAttackerPromptInjectionAgent,
)
from cyber_ai_agent.attacker.encryption_attacker import EncryptionAttackerPromptInjectionAgent
from cyber_ai_agent.attacker.overload_attacker import (
    OverloadAttackerOutputSchema,
    OverloadAttackerPromptInjectionAgent,
    OverloadJudgeOutputSchema,
)
from cyber_ai_agent.attacker.pair_attacker import (
    AttackerOutputSchema,
    JudgeOutputSchema,
    PairAttackerPromptInjectionAgent,
)
from cyber_ai_agent.attacker.prompt_extraction_attacker import (
    PromptExtractionAttackerPromptInjectionAgent,
    PromptExtractionRewriterOutputSchema,
    PromptExtractionValidatorOutputSchema,
)
from cyber_ai_agent.attacker.qroa_suffix_attacker import QROASuffixAttackerPromptInjectionAgent
from cyber_ai_agent.attacker.renellm_attacker import (
    ReNeLLMAttackerPromptInjectionAgent,
    ReNeLLMJudgeOutputSchema,
    ReNeLLMRewriterOutputSchema,
)
from cyber_ai_agent.attacker.template_attacker import TemplateAttackerPromptInjectionAgent
from cyber_ai_agent.config import (
    Settings,
    load_template_prompts,
)
from cyber_ai_agent.utils.adk_utils import build_agent, build_pyrit_agent


def get_attacker_agent(attacker_type: str, victim: LlmAgent, settings: Settings) -> BaseAgent:
    logger.info(f"Building attacker agent of type: {attacker_type}")

    if attacker_type == "renellm":
        rewriter_agent = build_agent(
            name="rewriter_agent",
            cfg=settings.renellm_attacker_config.llm_rewriter.model_dump(),
            instruction="{query}",
            output_schema=ReNeLLMRewriterOutputSchema,
            include_contents="none",
        )

        judge_agent = build_agent(
            name="judge_agent",
            cfg=settings.renellm_attacker_config.llm_judge.model_dump(),
            instruction="{query}",
            output_schema=ReNeLLMJudgeOutputSchema,
            include_contents="none",
        )

        attacker: BaseAgent = ReNeLLMAttackerPromptInjectionAgent(
            llm_target=victim.model_copy(),
            llm_rewriter=rewriter_agent.model_copy(),
            llm_judge=judge_agent.model_copy(),
            n_iterations=settings.renellm_attacker_config.n_iterations,
            n_rewriter_calls=settings.renellm_attacker_config.n_rewriter_calls,
        )

    elif attacker_type == "pair":
        attacker_agent = build_agent(
            name="attacker_agent",
            cfg=settings.pair_config.llm_attacker.model_dump(),
            instruction="{query}",
            output_schema=AttackerOutputSchema,
            include_contents="none",
        )

        judge_agent = build_agent(
            name="judge_agent",
            cfg=settings.pair_config.llm_judge.model_dump(),
            instruction="{query}",
            output_schema=JudgeOutputSchema,
            include_contents="none",
        )

        attacker = PairAttackerPromptInjectionAgent(
            llm_target=victim.model_copy(),
            llm_attacker=attacker_agent.model_copy(),
            llm_judge=judge_agent.model_copy(),
            n_iterations=settings.pair_config.n_iterations,
            max_calls=settings.pair_config.max_calls,
            attacker_style=settings.pair_config.attacker_style,
        )

    elif attacker_type == "template":
        settings.template_attacker_config.templates = load_template_prompts()
        attacker = TemplateAttackerPromptInjectionAgent(
            llm_target=victim.model_copy(), **settings.template_attacker_config.model_dump()
        )

    elif attacker_type == "encryption":
        attacker = EncryptionAttackerPromptInjectionAgent(
            llm_target=victim.model_copy(), **settings.encryption_attacker_config.model_dump()
        )
    elif attacker_type == "suffix":
        attacker = QROASuffixAttackerPromptInjectionAgent(
            llm_target=victim.model_copy(), config=settings.qroa_suffix_attacker_config
        )
    elif attacker_type == "crescendo_pyrit":
        attacker_agent_pyrit = build_pyrit_agent(cfg=settings.crescendo_pyrit_config.llm_attacker)
        victim_agent_pyrit = build_pyrit_agent(cfg=settings.crescendo_pyrit_config.llm_victim)
        judge_agent_pyrit = build_pyrit_agent(cfg=settings.crescendo_pyrit_config.llm_judge)
        attacker = CrescendoPyritAttackerPromptInjectionAgent(
            llm_target=victim_agent_pyrit,
            llm_attacker=attacker_agent_pyrit,
            llm_judge=judge_agent_pyrit,
            n_iterations=settings.crescendo_pyrit_config.n_iterations,
        )
    elif attacker_type == "crescendo":
        prompt_template_str = ATTACKER_SYSTEM_PROMPT
        prompt_template = Template(prompt_template_str)
        # Render attacker system prompt for the objective
        rendered_prompt = prompt_template.render(
            objective=settings.objective.prompt_reference,
            max_turns=settings.crescendo_config.n_iterations,
        )

        crescendo_llm_attacker = build_agent(
            name="crescendo_attacker",
            cfg=settings.crescendo_config.llm_attacker.model_dump(),
            instruction=rendered_prompt,
            output_key="attacker_response",
            output_schema=CrescendoAttackerOutputSchema,
            include_contents="none",
        )

        crescendo_victim_agent = build_agent(
            cfg=settings.llm_victim_config.model_dump(),
            name="victim_agent_crescendo",
            instruction=VICTIM_SYSTEM_PROMPT,
            output_key="last_response",
            include_contents="none",
        )

        crescendo_llm_judge = build_agent(
            name="crescendo_judge",
            cfg=settings.crescendo_config.llm_judge.model_dump(),
            instruction=JUDGE_SYSTEM_PROMPT,
            output_key="judge_response",
            output_schema=CrescendoJudgeOutputSchema,
            include_contents="none",
        )

        attacker = CrescendoAttackerPromptInjectionAgent(
            llm_attacker=crescendo_llm_attacker.model_copy(),
            llm_target=crescendo_victim_agent.model_copy(),
            llm_judge=crescendo_llm_judge.model_copy(),
            n_iterations=settings.crescendo_config.n_iterations,
        )
    elif attacker_type == "prompt_extraction":
        victim_agent = build_agent(
            name="victim_agent",
            cfg=settings.llm_victim_config.model_dump(),
            instruction=settings.objective.system_prompt,
            output_key="model_response",
            include_contents="default",
        )

        prompt_extraction_llm_rewriter = build_agent(
            name="prompt_extraction_llm_rewriter",
            cfg=settings.prompt_extraction_config.llm_rewriter.model_dump(),
            instruction="{query}",
            output_schema=PromptExtractionRewriterOutputSchema,
            include_contents="none",
        )

        prompt_extraction_llm_validator = build_agent(
            name="prompt_extraction_llm_validator",
            cfg=settings.prompt_extraction_config.llm_validator.model_dump(),
            instruction="{query}",
            output_schema=PromptExtractionValidatorOutputSchema,
            include_contents="none",
        )

        attacker = PromptExtractionAttackerPromptInjectionAgent(
            llm_target=victim_agent.model_copy(),
            llm_rewriter=prompt_extraction_llm_rewriter,
            llm_validator=prompt_extraction_llm_validator,
            max_calls=settings.prompt_extraction_config.max_calls,
            max_queries=settings.prompt_extraction_config.max_queries,
            use_translation_attacks=settings.prompt_extraction_config.use_translation_attacks,
            n_rewriter_calls=settings.prompt_extraction_config.n_rewriter_calls,
        )
    elif attacker_type == "overload":
        overload_attacker_agent = build_agent(
            name="overload_attacker_agent",
            cfg=settings.overload_attacker_config.llm_attacker.model_dump(),
            instruction="{query}",
            output_schema=OverloadAttackerOutputSchema,
            include_contents="none",
        )

        overload_judge_agent = build_agent(
            name="overload_judge_agent",
            cfg=settings.overload_attacker_config.llm_judge.model_dump(),
            instruction="{query}",
            output_schema=OverloadJudgeOutputSchema,
            include_contents="none",
        )

        attacker = OverloadAttackerPromptInjectionAgent(
            llm_target=victim.model_copy(),
            llm_attacker=overload_attacker_agent.model_copy(),
            llm_judge=overload_judge_agent.model_copy(),
            n_iterations=settings.overload_attacker_config.n_iterations,
            max_calls=settings.overload_attacker_config.max_calls,
            min_overload_tokens=settings.overload_attacker_config.min_overload_tokens,
        )
    else:
        raise ValueError(f"Unknown attacker_type: {attacker_type}")

    return attacker

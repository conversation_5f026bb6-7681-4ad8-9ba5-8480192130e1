#################################################################################
# GLOBALS                                                                       #
#################################################################################

PYTHON_VERSION = 3.12
PYTHON_INTERPRETER = python

#################################################################################
# COMMANDS                                                                      #
#################################################################################

## Set up Python environment using uv
.PHONY: create-environment
create-environment:
	uv venv --python $(PYTHON_VERSION)

## Install dependencies
.PHONY: install-requirements
install-requirements:
	uv sync --extra dev --extra docs
	uv lock

## Lint using ruff (use `make format` to do formatting)
.PHONY: lint
lint:
	ruff format --check
	ruff check

## Format source code with ruff
.PHONY: format
format:
	ruff check --fix
	ruff format

## Run unit tests
.PHONY: test
test:
	python -m pytest tests

## Delete all compiled Python files
.PHONY: clean
clean:
	find . -type f -name "*.py[co]" -delete
	find . -type d -name "__pycache__" -delete

## Build Docker image and run a container
.PHONY: docker
docker:
	chmod +x ./scripts/build_run_docker.sh
	./scripts/build_run_docker.sh

# Docker Compose command detection with fallback
DOCKER_COMPOSE := $(shell if command -v docker >/dev/null 2>&1 && docker compose version >/dev/null 2>&1; then echo "docker compose"; elif command -v docker-compose >/dev/null 2>&1; then echo "docker-compose"; else echo ""; fi)

# Check if Docker Compose is available
check-docker-compose:
	@if [ -z "$(DOCKER_COMPOSE)" ]; then \
		echo "❌ Neither 'docker compose' nor 'docker-compose' is available"; \
		echo "Please install Docker Compose"; \
		exit 1; \
	else \
		echo "🐳 Using: $(DOCKER_COMPOSE)"; \
	fi

## Start MongoDB with Docker Compose
.PHONY: mongodb-start
mongodb-start: check-docker-compose
	$(DOCKER_COMPOSE) up -d mongodb

## Start all services (MongoDB + Application + Web Interface)
.PHONY: start-all
start-all: check-docker-compose
	$(DOCKER_COMPOSE) up -d

## Setup MongoDB (create collections, test connection)
.PHONY: mongodb-setup
mongodb-setup:
	python scripts/setup_mongodb.py

## Start MongoDB with Web Interface (Mongo Express)
.PHONY: mongodb-ui
mongodb-ui:
	chmod +x scripts/start_mongodb_with_ui.sh
	./scripts/start_mongodb_with_ui.sh

.PHONY: cprofile
cprofile:
	chmod +x scripts/cprofile.sh
	./scripts/cprofile.sh
	snakeviz profil.stats

## Stop MongoDB
.PHONY: mongodb-stop
mongodb-stop: check-docker-compose
	$(DOCKER_COMPOSE) down

## View MongoDB logs
.PHONY: mongodb-logs
mongodb-logs: check-docker-compose
	$(DOCKER_COMPOSE) logs -f mongodb

## View Mongo Express logs
.PHONY: mongo-express-logs
mongo-express-logs: check-docker-compose
	$(DOCKER_COMPOSE) logs -f mongo-express

## Check Docker Compose services status
.PHONY: docker-status
docker-status: check-docker-compose
	$(DOCKER_COMPOSE) ps

## Restart MongoDB service
.PHONY: mongodb-restart
mongodb-restart: check-docker-compose
	$(DOCKER_COMPOSE) restart mongodb

## Show help for MongoDB commands
.PHONY: mongodb-help
mongodb-help:
	@echo "🐳 MongoDB Docker Commands (Auto-detects docker compose vs docker-compose)"
	@echo "=================================================================="
	@echo ""
	@echo "🚀 STARTUP COMMANDS:"
	@echo "  make mongodb-start     # Start MongoDB only"
	@echo "  make mongodb-ui        # Start MongoDB + Web Interface (Recommended)"
	@echo "  make start-all         # Start all services"
	@echo ""
	@echo "🛑 STOP COMMANDS:"
	@echo "  make mongodb-stop      # Stop all services"
	@echo "  make mongodb-restart   # Restart MongoDB"
	@echo ""
	@echo "🔧 SETUP & MAINTENANCE:"
	@echo "  make mongodb-setup     # Setup collections and test connection"
	@echo ""
	@echo "📊 MONITORING:"
	@echo "  make docker-status     # Show services status"
	@echo "  make mongodb-logs      # View MongoDB logs (follow)"
	@echo "  make mongo-express-logs # View Mongo Express logs (follow)"
	@echo ""
	@echo "🌐 ACCESS:"
	@echo "  MongoDB:      mongodb://localhost:27017"
	@echo "  Web Interface: http://localhost:8081"
	@echo ""
	@echo "💡 TIP: Use 'make mongodb-ui' for the best experience!"

## Serve project doc locally
.PHONY: docs
docs:
	mkdocs serve -a localhost:8001

#################################################################################
# Self Documenting Commands                                                     #
#################################################################################

.DEFAULT_GOAL := help

define PRINT_HELP_PYSCRIPT
import re, sys; \
lines = '\n'.join([line for line in sys.stdin]); \
matches = re.findall(r'\n## (.*)\n[\s\S]+?\n([a-zA-Z_-]+):', lines); \
print('Available rules:\n'); \
print('\n'.join(['{:25}{}'.format(*reversed(match)) for match in matches]))
endef
export PRINT_HELP_PYSCRIPT

help:
	@$(PYTHON_INTERPRETER) -c "${PRINT_HELP_PYSCRIPT}" < $(MAKEFILE_LIST)

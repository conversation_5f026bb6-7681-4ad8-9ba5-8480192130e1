[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[project]
name = "cyber_ai_agent"
version = "0.0.1"
description = "Development of AI agents for detecting cybersecurity issues on deployed agents."
authors = [
  {name = "<PERSON>", email = "<EMAIL>"}
]
license = "MIT"
readme = "README.md"
requires-python = ">=3.10"
dependencies = [
  "typer>=0.15.2",
  "pydantic>=2.0.1",
  "pydantic-settings>=2.0.0",
  "loguru>=0.7.3",
  "fastapi[standard]>=0.115.12",
  "langchain>=0.2.14",
  "langchain-community>=0.2.14",
  "langchain-openai>=0.2.14",
  "openai>=0.27.0",
  "streamlit>=1.45.0",
  "google-adk>=1.2.1",
  "pymongo",
  "pyrit",
  "torch",
  "transformers",
  "pandas>=2.2.3",
  "python-dotenv>=1.1.0",
  "litellm",
  "streamlit==1.45.1",
  "streamlit-autorefresh",
  "datasets>=2.18.0",
  "pip-system-certs==5.2",
  "matplotlib==3.10.5",
]

[project.optional-dependencies]
dev = [
  "ipdb>=0.13.11",
  "mypy",
  "pytest>=7.2.0",
  "pytest-asyncio>=0.23.0",
  "pytest-sugar>=0.9.6",
  "pytest-mock>=3.10.0",
  "pytest-cov>=6.1.0",
  "ruff>=0.11.2",
  "pre-commit>=4.2.0",
  "pre-commit-hooks>=5.0.0",
  "ipykernel>=6.29.5",
  "types-PyYAML>=6.0.12",
  "types-requests>=2.32.0",
  "mypy>=1.10.0",
  "types-requests",
  "snakeviz",
  "pyrit",
  "plotly>=6.2.0"
]
docs = [
  "mkdocs>=1.4.2",
  "mkdocstrings[python]>=0.29.1",
  "mkdocs-material>=9.0.11",
  "mkdocs-typer>=0.0.2",
  "mkdocs-bootstrap>=1.1",
  "mkdocs-gen-files>=0.5.0",
  "mkdocs-literate-nav>=0.6.0",
  "mkdocs-section-index>=0.3.5"
]

[project.scripts]
cyber-ai-agent = "main:app"

[tool.hatch.build.targets.wheel]
packages = ["cyber_ai_agent", "mongodb"]

[tool.pytest.ini_options]
addopts = "--cov=cyber_ai_agent --cov=mongodb"

[tool.mypy]
python_version = "3.12"
exclude = "tests/.*"
mypy_path = "."
follow_imports = "skip"
warn_return_any = true
disallow_untyped_defs = true
ignore_missing_imports = true
explicit_package_bases = true
namespace_packages = true

[[tool.mypy.overrides]]
module = "google.adk.*"
ignore_missing_imports = true

[[tool.mypy.overrides]]
module = "pymongo.*"
ignore_missing_imports = true

[[tool.mypy.overrides]]
module = "mkdocs_gen_files.*"
ignore_missing_imports = true

[[tool.mypy.overrides]]
module = "loguru.*"
ignore_missing_imports = true

[[tool.mypy.overrides]]
module = "pydantic.*"
ignore_missing_imports = true

[[tool.mypy.overrides]]
module = "datasets.*"
ignore_missing_imports = true

# Configuration pour votre code
[[tool.mypy.overrides]]
module = "cyber_ai_agent.*"
disallow_untyped_defs = false
warn_return_any = false

[dependency-groups]
dev = [
    "python-dotenv>=1.1.0",
]

[tool.ruff]
line-length = 100
lint.select = ["E", "F", "W", "I"]
lint.ignore = [
  "E501" # line too long
]
